import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import OptimizationEngine from '../OptimizationEngine.vue';

// Create a simple mock for the optimization store
const mockOptimizationStore = () => ({
  isOptimizing: false,
  optimizationResults: null,
  selectedSuggestions: [],
  optimizationSettings: {
    prioritizeSDST: true,
    prioritizeUrgency: true,
    prioritizeResourceUtilization: true,
    allowMinorDelays: true,
    maxDelayMinutes: 30,
    preserveEmergencies: true,
  },
  optimizationHistory: [],
  currentSuggestions: [],
  potentialSavings: {
    sdstReduction: 0,
    utilizationImprovement: 0,
    conflictReduction: 0,
    timesSaved: 0,
  },
  optimizationSummary: {
    totalSuggestions: 0,
    highPriority: 0,
    mediumPriority: 0,
    lowPriority: 0,
    estimatedImpact: 'Low',
  },
  canOptimize: true,
  runOptimization: vi.fn(),
  clearOptimizationResults: vi.fn(),
  updateOptimizationSettings: vi.fn(),
  toggleSuggestionSelection: vi.fn(),
  selectAllSuggestions: vi.fn(),
  clearAllSelections: vi.fn(),
  applySuggestions: vi.fn(),
});

// Mock the stores
vi.mock('@/stores/optimizationStore', () => ({
  useOptimizationStore: () => mockOptimizationStore()
}));

describe('OptimizationEngine', () => {
  let wrapper;

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    const pinia = createPinia();
    setActivePinia(pinia);

    wrapper = mount(OptimizationEngine, {
      global: {
        plugins: [pinia],
      },
    });
  });

  it('renders correctly', () => {
    expect(wrapper.find('h1').text()).toBe('Schedule Optimization Engine');
    expect(wrapper.find('.optimization-controls').exists()).toBe(true);
  });

  it('renders optimization settings', () => {
    // Check for specific settings
    expect(wrapper.text()).toContain('Prioritize SDST Reduction');
    expect(wrapper.text()).toContain('Respect Surgery Urgency');
    expect(wrapper.text()).toContain('Optimize Resource Utilization');
    expect(wrapper.text()).toContain('Allow Minor Delays');
  });

  it('shows run optimization button', () => {
    const runButton = wrapper.find('.run-optimization-btn');
    expect(runButton.exists()).toBe(true);
  });

  it('shows no results state initially', () => {
    expect(wrapper.find('.no-results').exists()).toBe(true);
    expect(wrapper.text()).toContain('Ready to Optimize');
    expect(wrapper.text()).toContain('Reduce Surgery-to-Surgery Transition (SDST) times');
  });

});
