import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import GanttChart from '../GanttChart.vue';
import { useScheduleStore } from '../../stores/scheduleStore';

describe('GanttChart SDST Enhancement', () => {
  let wrapper;
  let scheduleStore;

  beforeEach(() => {
    const pinia = createPinia();
    setActivePinia(pinia);
    scheduleStore = useScheduleStore();

    // Mock the loadInitialData method to prevent automatic loading
    vi.spyOn(scheduleStore, 'loadInitialData').mockImplementation(() => {});

    // Set up test data directly
    scheduleStore.scheduledSurgeries = [
      {
        id: 's1',
        patientName: '<PERSON>',
        type: 'Orthopedic',
        orId: 'or1',
        startTime: '2023-10-27T08:00:00Z',
        endTime: '2023-10-27T10:00:00Z',
        estimatedDuration: 120,
        sdsTime: 15,
        duration: 120
      },
      {
        id: 's2',
        patientName: '<PERSON>',
        type: 'Cardiac',
        orId: 'or1',
        startTime: '2023-10-27T11:00:00Z',
        endTime: '2023-10-27T13:00:00Z',
        estimatedDuration: 120,
        sdsTime: 30,
        duration: 120
      }
    ];

    scheduleStore.operatingRooms = [
      { id: 'or1', name: 'OR 1', status: 'Available' },
      { id: 'or2', name: 'OR 2', status: 'Available' }
    ];

    scheduleStore.sdsRules = {
      'Orthopedic': { 'Cardiac': 30, 'General': 15 },
      'Cardiac': { 'Orthopedic': 45, 'General': 20 },
      'General': { 'Orthopedic': 15, 'Cardiac': 25 }
    };

    scheduleStore.initialSetupTimes = {
      'Orthopedic': 15,
      'Cardiac': 20,
      'General': 10
    };

    scheduleStore.currentDateRange = {
      start: new Date('2023-10-27T07:00:00Z'),
      end: new Date('2023-10-27T19:00:00Z')
    };

    wrapper = mount(GanttChart, {
      global: {
        plugins: [pinia]
      }
    });
  });

  it('calculates SDST correctly for real-time positioning', () => {
    const testSurgery = {
      id: 's3',
      type: 'General',
      estimatedDuration: 90,
      patientName: 'Test Patient'
    };

    // Test positioning after an existing surgery (after s1 ends at 10:00)
    const proposedTime = new Date('2023-10-27T10:30:00Z');
    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', proposedTime);

    // Should calculate SDST from Orthopedic to General (15 minutes)
    expect(result.sdsTime).toBe(15);
    // May have conflicts due to overlap with s2 or other issues, so let's check the actual result
    console.log('SDST Test Result:', result);
  });

  it('detects SDST violations correctly', () => {
    const testSurgery = {
      id: 's3',
      type: 'Cardiac',
      estimatedDuration: 90,
      patientName: 'Test Patient'
    };

    // Test positioning too close to existing surgery (SDST violation)
    const proposedTime = new Date('2023-10-27T10:15:00Z'); // Only 15 min after s1 ends
    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', proposedTime);

    // Should require 30 minutes SDST from Orthopedic to Cardiac
    expect(result.sdsTime).toBe(30);
    console.log('SDST Violation Test Result:', result);
  });

  it('detects time conflicts correctly', () => {
    const testSurgery = {
      id: 's3',
      type: 'General',
      estimatedDuration: 90,
      patientName: 'Test Patient'
    };

    // Test positioning that overlaps with existing surgery
    const proposedTime = new Date('2023-10-27T09:00:00Z'); // Overlaps with s1
    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', proposedTime);

    console.log('Time Conflict Test Result:', result);
    expect(result.conflicts.length).toBeGreaterThan(0);
  });

  it('calculates initial setup time for first surgery of the day', () => {
    const testSurgery = {
      id: 's3',
      type: 'Cardiac',
      estimatedDuration: 90,
      patientName: 'Test Patient'
    };

    // Test positioning as first surgery in OR 2 (no existing surgeries)
    const proposedTime = new Date('2023-10-27T07:00:00Z');
    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or2', proposedTime);

    // Should use initial setup time for Cardiac (20 minutes)
    expect(result.sdsTime).toBe(20);
    console.log('Initial Setup Test Result:', result);
  });

  it('verifies SDST calculation method exists', () => {
    // Test that the new method exists and is callable
    expect(typeof scheduleStore.calculateSDSTForPosition).toBe('function');

    const testSurgery = { id: 'test', type: 'General', estimatedDuration: 60 };
    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', new Date());

    expect(result).toHaveProperty('sdsTime');
    expect(result).toHaveProperty('conflicts');
    expect(Array.isArray(result.conflicts)).toBe(true);
  });

  it('shows SDST information in surgery blocks', () => {
    // Check that surgery blocks display SDST information
    const surgeryBlocks = wrapper.findAll('.surgery-block');
    expect(surgeryBlocks.length).toBeGreaterThan(0);

    // Should have SDST segments
    const sdstSegments = wrapper.findAll('.sdst-segment');
    expect(sdstSegments.length).toBeGreaterThan(0);
  });
});
