import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import OptimizationSuggestions from '../OptimizationSuggestions.vue';
import { useOptimizationStore } from '@/stores/optimizationStore';

// Mock the stores
vi.mock('@/stores/optimizationStore');

describe('OptimizationSuggestions', () => {
  let wrapper;
  let optimizationStore;

  beforeEach(() => {
    // Create a fresh Pinia instance for each test
    const pinia = createPinia();
    setActivePinia(pinia);

    // Mock the optimization store
    optimizationStore = {
      isOptimizing: false,
      optimizationResults: null,
      currentSuggestions: [],
      runOptimization: vi.fn(),
      applySuggestions: vi.fn(),
    };

    // Mock the useOptimizationStore function
    useOptimizationStore.mockReturnValue(optimizationStore);

    wrapper = mount(OptimizationSuggestions, {
      global: {
        plugins: [pinia],
        stubs: {
          'router-link': {
            template: '<a><slot /></a>',
            props: ['to']
          }
        }
      },
    });
  });

  it('renders correctly', () => {
    expect(wrapper.find('.optimization-suggestions').exists()).toBe(true);
    expect(wrapper.find('h3').text()).toContain('Optimization Suggestions');
  });

  it('shows initial state when no optimization results', () => {
    expect(wrapper.find('.initial-state').exists()).toBe(true);
    expect(wrapper.text()).toContain('Ready to Optimize');
    expect(wrapper.text()).toContain('Reduce SDST times');
    expect(wrapper.text()).toContain('Improve resource utilization');
    expect(wrapper.text()).toContain('Resolve conflicts');
  });

  it('shows quick optimize button in initial state', () => {
    const quickBtn = wrapper.find('.quick-optimize-btn');
    expect(quickBtn.exists()).toBe(true);
    expect(quickBtn.text()).toBe('Quick Optimize');
    expect(quickBtn.attributes('disabled')).toBeUndefined();
  });

  it('shows view all link', () => {
    const viewAllLink = wrapper.find('.view-all-link');
    expect(viewAllLink.exists()).toBe(true);
    expect(viewAllLink.text()).toBe('View All');
  });

  it('calls runOptimization when quick optimize button is clicked', async () => {
    const quickBtn = wrapper.find('.quick-optimize-btn');
    await quickBtn.trigger('click');
    
    expect(optimizationStore.runOptimization).toHaveBeenCalled();
  });

  it('shows loading state when optimizing', async () => {
    optimizationStore.isOptimizing = true;
    await wrapper.vm.$nextTick();
    
    expect(wrapper.find('.loading-state').exists()).toBe(true);
    expect(wrapper.text()).toContain('Analyzing schedule for optimization opportunities...');
    expect(wrapper.find('.loading-spinner').exists()).toBe(true);
  });

  it('disables quick optimize button when optimizing', async () => {
    optimizationStore.isOptimizing = true;
    await wrapper.vm.$nextTick();
    
    const quickBtn = wrapper.find('.quick-optimize-btn');
    expect(quickBtn.attributes('disabled')).toBeDefined();
    expect(quickBtn.text()).toContain('Analyzing...');
  });

  it('shows suggestions when available', async () => {
    const mockSuggestions = [
      {
        id: 'test-1',
        type: 'reorder',
        category: 'SDST Optimization',
        priority: 'High',
        title: 'Test Suggestion 1',
        description: 'Test description 1',
        impact: 30,
        effort: 'Low',
        estimatedSavings: '30 minutes',
        risks: [],
      },
      {
        id: 'test-2',
        type: 'relocate',
        category: 'Resource Optimization',
        priority: 'Medium',
        title: 'Test Suggestion 2',
        description: 'Test description 2',
        impact: 20,
        effort: 'Medium',
        estimatedSavings: '20 minutes',
        risks: ['Minor risk'],
      }
    ];

    optimizationStore.optimizationResults = { suggestions: mockSuggestions };
    optimizationStore.currentSuggestions = mockSuggestions;
    
    await wrapper.vm.$nextTick();
    
    expect(wrapper.find('.suggestions-list').exists()).toBe(true);
    
    const suggestionItems = wrapper.findAll('.suggestion-item');
    expect(suggestionItems.length).toBe(2);
    
    // Check first suggestion
    expect(suggestionItems[0].text()).toContain('Test Suggestion 1');
    expect(suggestionItems[0].text()).toContain('Test description 1');
    expect(suggestionItems[0].text()).toContain('30 min');
    expect(suggestionItems[0].classes()).toContain('priority-high');
    
    // Check second suggestion
    expect(suggestionItems[1].text()).toContain('Test Suggestion 2');
    expect(suggestionItems[1].text()).toContain('Test description 2');
    expect(suggestionItems[1].text()).toContain('20 min');
    expect(suggestionItems[1].classes()).toContain('priority-medium');
  });

  it('limits suggestions to top 3', async () => {
    const mockSuggestions = Array.from({ length: 5 }, (_, i) => ({
      id: `test-${i + 1}`,
      type: 'reorder',
      category: 'SDST Optimization',
      priority: 'High',
      title: `Test Suggestion ${i + 1}`,
      description: `Test description ${i + 1}`,
      impact: 30 - i * 5,
      effort: 'Low',
      estimatedSavings: `${30 - i * 5} minutes`,
      risks: [],
    }));

    optimizationStore.optimizationResults = { suggestions: mockSuggestions };
    optimizationStore.currentSuggestions = mockSuggestions;
    
    await wrapper.vm.$nextTick();
    
    const suggestionItems = wrapper.findAll('.suggestion-item');
    expect(suggestionItems.length).toBe(3); // Should only show top 3
  });

  it('shows no suggestions state when optimization results exist but no suggestions', async () => {
    optimizationStore.optimizationResults = { suggestions: [] };
    optimizationStore.currentSuggestions = [];
    
    await wrapper.vm.$nextTick();
    
    expect(wrapper.find('.no-suggestions').exists()).toBe(true);
    expect(wrapper.text()).toContain('Schedule Optimized!');
    expect(wrapper.text()).toContain('No immediate improvements found');
    expect(wrapper.find('.success-icon').exists()).toBe(true);
  });

  it('handles apply suggestion correctly', async () => {
    const mockSuggestion = {
      id: 'test-1',
      type: 'reorder',
      category: 'SDST Optimization',
      priority: 'High',
      title: 'Test Suggestion',
      description: 'Test description',
      impact: 30,
      effort: 'Low',
      estimatedSavings: '30 minutes',
      risks: [],
    };

    optimizationStore.optimizationResults = { suggestions: [mockSuggestion] };
    optimizationStore.currentSuggestions = [mockSuggestion];
    
    await wrapper.vm.$nextTick();
    
    const applyBtn = wrapper.find('.apply-btn');
    expect(applyBtn.exists()).toBe(true);
    
    await applyBtn.trigger('click');
    
    expect(optimizationStore.applySuggestions).toHaveBeenCalledWith(['test-1']);
  });

  it('shows priority badges correctly', async () => {
    const mockSuggestions = [
      {
        id: 'high-priority',
        priority: 'High',
        title: 'High Priority Suggestion',
        description: 'High priority description',
        impact: 30,
        effort: 'Low',
        estimatedSavings: '30 minutes',
        risks: [],
      },
      {
        id: 'medium-priority',
        priority: 'Medium',
        title: 'Medium Priority Suggestion',
        description: 'Medium priority description',
        impact: 20,
        effort: 'Medium',
        estimatedSavings: '20 minutes',
        risks: [],
      },
      {
        id: 'low-priority',
        priority: 'Low',
        title: 'Low Priority Suggestion',
        description: 'Low priority description',
        impact: 10,
        effort: 'Low',
        estimatedSavings: '10 minutes',
        risks: [],
      }
    ];

    optimizationStore.optimizationResults = { suggestions: mockSuggestions };
    optimizationStore.currentSuggestions = mockSuggestions;
    
    await wrapper.vm.$nextTick();
    
    const priorityBadges = wrapper.findAll('.priority-badge');
    expect(priorityBadges.length).toBe(3);
    
    expect(priorityBadges[0].classes()).toContain('priority-high');
    expect(priorityBadges[0].text()).toBe('High');
    
    expect(priorityBadges[1].classes()).toContain('priority-medium');
    expect(priorityBadges[1].text()).toBe('Medium');
    
    expect(priorityBadges[2].classes()).toContain('priority-low');
    expect(priorityBadges[2].text()).toBe('Low');
  });

  it('shows impact values correctly', async () => {
    const mockSuggestion = {
      id: 'test-1',
      priority: 'High',
      title: 'Test Suggestion',
      description: 'Test description',
      impact: 45,
      effort: 'Low',
      estimatedSavings: '45 minutes SDST reduction',
      risks: [],
    };

    optimizationStore.optimizationResults = { suggestions: [mockSuggestion] };
    optimizationStore.currentSuggestions = [mockSuggestion];
    
    await wrapper.vm.$nextTick();
    
    const impactElement = wrapper.find('.impact');
    expect(impactElement.text()).toBe('45 min');
    
    const savingsElement = wrapper.find('.savings');
    expect(savingsElement.text()).toBe('45 minutes SDST reduction');
  });

  it('handles async apply suggestion with loading state', async () => {
    const mockSuggestion = {
      id: 'test-1',
      priority: 'High',
      title: 'Test Suggestion',
      description: 'Test description',
      impact: 30,
      effort: 'Low',
      estimatedSavings: '30 minutes',
      risks: [],
    };

    optimizationStore.optimizationResults = { suggestions: [mockSuggestion] };
    optimizationStore.currentSuggestions = [mockSuggestion];
    
    // Mock applySuggestions to return a promise
    optimizationStore.applySuggestions.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );
    
    await wrapper.vm.$nextTick();
    
    const applyBtn = wrapper.find('.apply-btn');
    
    // Click the button
    const clickPromise = applyBtn.trigger('click');
    
    // Button should be disabled during async operation
    await wrapper.vm.$nextTick();
    expect(applyBtn.attributes('disabled')).toBeDefined();
    
    // Wait for the async operation to complete
    await clickPromise;
    await wrapper.vm.$nextTick();
    
    // Button should be enabled again
    expect(applyBtn.attributes('disabled')).toBeUndefined();
  });
});
